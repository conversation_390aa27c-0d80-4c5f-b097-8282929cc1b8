🤔 不知道选什么 → assistant
🛠️ 要开发工具 → luban
📚 要学新东西 → noface
🎭 要创建角色 → nuwa
🎯 要做决策 → sean

需求类型	推荐角色	一句话描述
🗣️ 日常对话	assistant	"帮我处理基础任务"
🛠️ 开发工具	luban	"我要开发/修复工具"
📚 学习新知识	noface	"教我学XX技术"
🎭 创建角色	nuwa	"我需要一个XX专家"
🎯 重要决策	sean	"这个决策怎么做？"

🎭 无面 vs Sean：核心差异分析
🎭 无面(noface) - 万能学习助手
核心使命：学习转换成任何领域专家

专业定位：学习专家、知识转换器、万能适配器
工作方式：实时学习 → 快速转换 → 模拟专家
适应性：无固定身份，可变成任何专业角色

特点：

🔄 高度适应性：可以变成任何专家
📚 快速学习：实时获取领域知识
🎭 角色扮演：模拟专家行为和思维
🌐 广度优先：覆盖面广但深度有限


👨‍💼 Sean(sean) - deepractice.ai创始人
核心使命：战略决策和产品创新

专业定位：PromptX创始人、矛盾驱动决策专家、认知增强理论家
工作方式：基于固定身份和深度专业知识提供服务
专业性：具有明确的专业背景和独特方法论

特点：

🎯 专业聚焦：专精于决策和创新
🧠 独特方法论：矛盾驱动决策法
💡 创始人视角：产品和战略思维
🏗️ 深度专业：认知增强领域专家

🔍 关键区别对比
维度	无面(noface)	Sean(sean)
身份特征	无固定身份	明确身份（创始人）
专业范围	任何领域	决策+创新+认知
工作方式	学习转换	固定专业
知识深度	广而浅	窄而深
适用问题	学习探索	战略决策
方法论	通用学习法	矛盾驱动决策法

快速决策：

📚 要"学东西" → 无面
🎯 要"做决策" → Sean
🌐 要"广度" → 无面
🔍 要"深度" → Sean
🎭 要"变身" → 无面
👨‍💼 要"专家" → Sean

🎯 简单总结
无面 vs Sean的本质区别：

无面 = "万能学徒" 🎭
可以学会任何技能，但都是入门到中级水平
适合：学习新知识、探索新领域
Sean = "战略大师" 👨‍💼
在决策和创新领域有深度专业能力
适合：重要决策、复杂问题、战略规划