[![@deepracticexs](https://avatars.githubusercontent.com/u/9959970?u=f7b866426b6eb79141b17fe34763037bdf197192&v=4&size=80)](https://github.com/deepracticexs)

## Description

## 工具发现与使用的四阶段认知流程设计

## 🎯 设计背景

PromptX当前面临工具资源类型区分的架构挑战：

- 提示词资源（.md）：AI学习消化，获得能力
- 代码资源（.js）：AI调用执行，产生动作

传统方案需要重构整个资源架构，成本高、风险大。

## 💡 解决方案：文档驱动的工具认知流程

采用"说明书优先"的设计理念，通过最小化改动实现优雅的类型区分。

### 核心原则

1. **文档驱动发现** - 只发现和展示 `.tool.md` 文件
2. **学习后使用** - AI必须先学习工具说明书，再调用工具
3. **源码定位** - 通过 `<source>` 标签指向实际可执行代码
4. **认知缓存** - 利用AI上下文作为工具知识缓存

## 🔄 四阶段认知流程

### 阶段1：工具发现

promptx_welcome  # 展示可用工具列表

**输出示例**：

```
🔧 项目工具
1. calculator - 数学计算工具，支持基本四则运算
2. image-processor - 图像处理工具，支持缩放、裁剪、滤镜
3. data-analyzer - 数据分析工具，支持统计和可视化
```

### 阶段2：学习理解（按需触发）

promptx_learn @tool://calculator

**AI行为**：

- 阅读工具的完整说明文档
- 理解功能、参数、使用方法
- 获取源码位置信息（`<source>` 标签）
- 缓存工具知识到当前对话上下文

### 阶段3：上下文检查

**AI自主判断**：

- 检查当前对话中是否已学习过该工具
- 如果已有工具知识，跳过学习步骤
- 避免重复学习同一工具

### 阶段4：工具调用

promptx_tool {
  "tool_resource": "@file://tools/calculator.tool.js", 
  "parameters": {"operation": "add", "a": 25, "b": 37}
}

**AI行为**：

- 使用从说明书中获得的源码位置
- 传递正确的参数格式
- 处理执行结果

## 📋 工具说明书标准格式

# Calculator Tool

> 数学计算工具，支持基本四则运算和高级数学函数
## 功能描述
提供加减乘除、幂运算、三角函数等数学计算能力，适用于各种数值计算场景。

## 参数说明
- `operation`: 运算类型 (add, subtract, multiply, divide, power, sin, cos, tan)
- `a`: 第一个操作数 (number)
- `b`: 第二个操作数 (number, 单参数运算时可选)

## 使用示例
```json
{"operation": "add", "a": 25, "b": 37}
{"operation": "power", "a": 2, "b": 8}  
{"operation": "sin", "a": 1.57}

## 返回格式

{"result": 62, "operation": "add", "operands": [25, 37]}

## 

[@file](https://github.com/file)://tools/calculator.tool.js

```

## 🌟 设计优势

### 🧠 认知符合性

- **先理解后使用** - 符合人类使用工具的认知模式
- **按需学习** - 只学习当前任务需要的工具
- **避免重复** - 已学习的工具不需要重复阅读

### 🏗️ 架构优雅性

- **职责分离** - welcome负责发现，learn负责理解，tool负责执行
- **最小改动** - 无需重构现有资源架构
- **向后兼容** - 现有工具只需添加说明文档

### 🔧 实现友好

- **缓存友好** - AI上下文天然缓存工具知识
- **错误减少** - 先学习再使用，减少参数错误
- **位置灵活** - 源码可以在任何地方（本地、网络、包内）

## 🚀 实施计划

### Phase 1: 基础设施

- 修改 `WelcomeCommand` 只发现 `.tool.md` 文件
    
    确保 `promptx_learn` 支持 `@tool://` 协议
    

### Phase 2: 标准化

### Phase 3: 测试验证

## 🎯 预期效果

1. **开发者体验**：必须先写文档才能被发现，促进工具文档化
2. **AI使用体验**：理解工具后再使用，调用更准确
3. **架构清晰度**：职责分离明确，扩展性强
4. **维护成本**：最小改动实现最大效果

## 📚 相关资源

- [DPML 资源协议框架](https://github.com/Deepractice/PromptX/issues/resource/protocol/tag/resource.tag.md)
- [PromptX 工具架构设计](https://github.com/Deepractice/PromptX/issues/docs/tool-architecture.md)
- [工具开发最佳实践](https://github.com/Deepractice/PromptX/issues/docs/tool-development.md)

---

**设计理念**：让AI像人一样，先学习工具说明书，再正确使用工具。