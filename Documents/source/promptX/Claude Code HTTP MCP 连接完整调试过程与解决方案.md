## 💡 背景

在开发 PromptX HTTP MCP 服务器时，遇到了与 Claude Code 连接的一系列问题。经过完整调试，总结出标准的解决流程，分享给社区开发者。

## 🔍 调试历程

### 阶段 1: 高级 API 问题 → 改用低级 API

**问题**: 最初使用 MCP SDK 的 `McpServer` 高级 API + `server.tool()` 方法

**现象**: HTTP 模式参数传递有问题，收到 `{"signal":{},"sessionId":"..."}` 而非实际参数

**解决**: 改用 `Server` 低级 API + `setRequestHandler()` 方法

// 修改前：高级 API（有 bug）
const server = new McpServer({...}, {...});
server.tool("promptx_init", schema, handler);

// 修改后：低级 API（稳定）
const server = new Server({...}, {...});
server.setRequestHandler(CallToolRequestSchema, async (request) => {
  const { name, arguments: args } = request.params;
  return await this.callTool(name, args || {});
});

### 阶段 2: 连接问题 → 修复监听地址

**问题**: 服务器只监听 `localhost`，Claude Code 无法连接

**现象**: 服务器启动正常，健康检查通过，但 Claude Code 连接失败

**解决**: 修改监听地址为 `0.0.0.0`

# scripts/mcp-server.sh 修改
SERVER_HOST="0.0.0.0"  # 原来是 "localhost"

### 阶段 3: 404 错误 → 添加 OAuth 支持

**问题**: Claude Code 报告 `Dynamic client registration failed: HTTP 404`

**原因**: Claude Code 需要 RFC7591 动态客户端注册支持

**解决**: 添加完整的 OAuth 端点支持

// 添加 OAuth 支持端点
app.get('/.well-known/oauth-authorization-server', this.handleOAuthMetadata.bind(this));
app.get('/.well-known/openid-configuration', this.handleOAuthMetadata.bind(this));
app.post('/register', this.handleDynamicRegistration.bind(this));
app.get('/authorize', this.handleAuthorize.bind(this));
app.post('/token', this.handleToken.bind(this));

## 🛠️ 完整解决方案

### 1. 使用低级 MCP API

const { Server } = require('@modelcontextprotocol/sdk/server/index.js');
const { ListToolsRequestSchema, CallToolRequestSchema } = require('@modelcontextprotocol/sdk/types.js');

const server = new Server({
  name: this.name,
  version: this.version
}, {
  capabilities: { tools: {} }
});

// 注册工具列表处理程序
server.setRequestHandler(ListToolsRequestSchema, async () => {
  return { tools: this.getToolDefinitions() };
});

// 注册工具调用处理程序  
server.setRequestHandler(CallToolRequestSchema, async (request) => {
  const { name, arguments: args } = request.params;
  return await this.callTool(name, args || {});
});

### 2. 配置正确的监听地址

# 服务器监听所有网络接口
SERVER_HOST="0.0.0.0"
SERVER_PORT=3000

# 启动命令
node src/bin/promptx.js mcp-server --transport http --port 3000 --host 0.0.0.0

### 3. 实现 OAuth 动态注册

// OAuth 元数据端点
handleOAuthMetadata(req, res) {
  const baseUrl = `http://${req.get('host')}`;
  res.json({
    issuer: baseUrl,
    authorization_endpoint: `${baseUrl}/authorize`,
    token_endpoint: `${baseUrl}/token`,
    registration_endpoint: `${baseUrl}/register`,
    response_types_supported: ["code"],
    grant_types_supported: ["authorization_code"],
    code_challenge_methods_supported: ["S256"],
    client_registration_types_supported: ["dynamic"]
  });
}

// 动态客户端注册
handleDynamicRegistration(req, res) {
  const clientId = `promptx-client-${Date.now()}`;
  res.json({
    client_id: clientId,
    client_secret: "not-required",
    registration_access_token: `reg-token-${Date.now()}`,
    registration_client_uri: `http://${req.get('host')}/register/${clientId}`,
    client_id_issued_at: Math.floor(Date.now() / 1000),
    client_secret_expires_at: 0
  });
}

## ✅ 验证步骤

# 1. 启动服务器
sh scripts/mcp-server.sh start

# 2. 测试 OAuth 元数据
curl http://localhost:3000/.well-known/oauth-authorization-server

# 3. 测试动态注册
curl -X POST http://localhost:3000/register -H "Content-Type: application/json" -d '{}'

# 4. 测试 MCP 工具
node scripts/test-http-mcp-client.js tools/list

# 5. Claude Code 连接测试
# 在 Claude Code 中配置: http://localhost:3000/mcp

## 🎯 最终效果

- ✅ **Claude Code 连接成功**: 无任何错误，正常建立 MCP 连接
- ✅ **所有 PromptX 工具可用**: init, welcome, action, learn, remember, recall, tool
- ✅ **参数传递正确**: HTTP 和 stdio 模式功能完全一致
- ✅ **OAuth 流程完整**: 支持动态客户端注册规范

## 💡 关键经验

1. **MCP SDK 的高级 API 在 HTTP 模式有 bug**，建议使用低级 API
2. **网络监听必须是 `0.0.0.0`**，不能是 `localhost`
3. **Claude Code 严格要求 OAuth 动态注册**，需要实现完整的 OAuth 端点
4. **调试顺序很重要**：API → 网络 → 协议，逐层解决

## 🔗 相关代码

- **HTTP 服务器**: `src/lib/mcp/MCPServerHttpCommand.js`
- **测试脚本**: `scripts/test-http-mcp-client.js`
- **服务管理**: `scripts/mcp-server.sh`

希望这个完整的调试过程能帮助其他开发者快速解决 Claude Code HTTP MCP 集成问题！