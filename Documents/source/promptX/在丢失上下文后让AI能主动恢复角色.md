[![@deepracticexs](https://avatars.githubusercontent.com/u/9959970?u=f7b866426b6eb79141b17fe34763037bdf197192&v=4&size=80)](https://github.com/deepracticexs)

## Description

[![@deepracticexs](https://avatars.githubusercontent.com/u/9959970?u=f7b866426b6eb79141b17fe34763037bdf197192&v=4&size=48)](https://github.com/deepracticexs)

## 不忘初心

通过工作记忆（上下文感知）让 AI 自己评估是不是上下文不太够了，然后在快丢失的时候进行恢复

## 记忆碎片

还有一个方案是设置某些 线索，如果 ai 看到这个线索，但是不知道这个线索的意思，就让他执行恢复 mcp，这个方案应该也可以，这个叫“记忆碎片”

## Activity

[![zhaomingc](https://avatars.githubusercontent.com/u/39235433?v=4&size=80)](https://github.com/zhaomingc)

### zhaomingc commented on Jun 18, 2025

[![@zhaomingc](https://avatars.githubusercontent.com/u/39235433?v=4&size=48)](https://github.com/zhaomingc)

分享一下我的处理思路：

## AI对话系统的核心问题：

- 上下文窗口有限制（如GPT-4的128K tokens）
- 长对话会导致早期信息被截断
- AI失去对话历史记忆，影响工作连续性
- 用户无法感知上下文截断何时发生

## 通过"验证密钥"机制检测上下文完整性：

1. 在新对话开始时显示50个验证密钥
2. 从第10轮开始定期验证AI是否还记得密钥
3. 如果AI答错，说明上下文已被截断
4. 自动提醒用户开启新对话窗口

## 设计原理：

- 随机性强 - 密钥值无规律，AI无法猜测
- 数量充足 - 50个密钥支持60+轮对话（循环使用）
- 简短易记 - 4字符长度，不占用太多上下文

## 工作机制：

- 后台运行 - 独立线程，不影响主流程
- 定期检查 - 每2秒检查一次对话状态
- 异常处理 - 错误不会中断监测服务

## 工作流程

1. 新对话开始  
    ↓
2. 自动显示50个验证密钥  
    ↓
3. AI记忆所有密钥  
    ↓
4. 对话进行中（第1-9轮）  
    ↓
5. 第10轮开始验证  
    ↓
6. 系统询问：K01=？  
    ↓
7. AI回答：A7B2  
    ↓
8. 验证结果：  
    ├─ 正确 → 继续对话  
    └─ 错误 → 发送截断警告

[![hillsmao](https://avatars.githubusercontent.com/u/107061250?s=64&u=e4fbd2579b53078546ed0438d7c9bb4773e32955&v=4)](https://github.com/hillsmao)

## Add a comment

new Comment

Markdown input: edit mode selected.