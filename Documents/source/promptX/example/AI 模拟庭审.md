本文出自深度实践 deepractice.ai

社区成员@明易（经其本人同意后发表）

亲身以一名法律工作者视角，分享 AI 应用于专业领域的真实案例！

---

## 1️⃣ 新手律师的痛点

大家好！我是明易，一名法律新人。

据我观察，新手律师在庭审中常常会遇到以下的问题：

- 庭审经验不足：
    

刚入行缺乏实战机会，只能"纸上谈兵"

- 辩论技巧薄弱：
    

面对资深律师时常被"碾压"，论证缺乏锐度

- 案例分析浅显：
    

停留在法条背诵层面，缺乏深度思考

那么，是否可以借助AI来庭审律师的实战能力呢？答案是可以的！

经过一段时间的打磨，我将模拟庭审做出来了，大家先感受一下：

![图片](https://mmbiz.qpic.cn/sz_mmbiz_jpg/G9EbG9MtFaiaRvF8t8JZ2PiaPlvx5udQOkkWkggHfAysoGu9UMHEb4UHsI24QqoibKSTaQfic77apY0KA5Xian12DLA/640?wx_fmt=jpeg&watermark=1&tp=webp&wxfrom=5&wx_lazy=1)

![图片](https://mmbiz.qpic.cn/sz_mmbiz_png/G9EbG9MtFaiaRvF8t8JZ2PiaPlvx5udQOk8o8YOdj0Y2ibBKk1dMKsLC6OdZJViaZ55KXSeXjTA1kfDfPcQSpaMlmQ/640?wx_fmt=png&from=appmsg&watermark=1&tp=webp&wxfrom=5&wx_lazy=1)

各位朋友可能不是法律人士，但从上图也不难看出，即便是非常小的问题，AI律师都可以有理有据、辩论不休。

说实话，这个辩论程度远比真实庭审还充分。

那么，我是怎么做出来的呢？这篇文章会带着大家一起揭秘。

2️⃣ 我的预期，以及被现实打脸

我的设想是：

1. 将案件材料投喂给AI

2. 让AI法官、书记员、以及各方当事人的律师，组成模拟法庭

3. 法官主持模拟庭审，决定当下由谁发言，其他人都必须遵守法官指令

4. 庭审流程的顺序是：

- 原告宣读起诉状
    
- 各被告对原告的起诉状进行回应
    
- 法庭质证环节
    
- 法庭辩论环节（包括自由辩论）
    
- 最后陈述环节  
    

5. 书记员做庭审笔录

6. 模拟庭审尽量把所有可能的问题都暴露出来

7. 庭审后增加“法官访谈”环节，针对本案事实或法律问题对法官提问

但很快地，我就发现存在以下的问题：

- AI角色平淡如水：
    
    普通AI机械式回应，缺乏真实对抗感
    
- 论证深度不够：
    
    无法形成完整的法理推导链条
    
- 专业性不足：
    
    不能体现多角色的专业和利益立场  
    

看着粗制滥造的模拟庭审，我甚至都产生自我怀疑。就在此时，朋友给我推荐了**PromptX**！

  
3️⃣ PromptX 的革命性突破

不难发现，导致我的模拟庭审不及预期的原因，就是AI生成的角色过于机械化，远远无法达到真人辩论的感觉。

但这恰是 **PromptX** 的强项！其非常强大的角色：**“女蜗”**，支持自定义角色。

为此，我第一时间就手搓了法官、律师、书记员等角色。

![图片](https://mmbiz.qpic.cn/sz_mmbiz_png/G9EbG9MtFaiaRvF8t8JZ2PiaPlvx5udQOkYIxpzVyRdb3LMk515u3lpLSnYJ6Y0FVBoW001ShOs5QiaCbFSKPibDzA/640?wx_fmt=png&from=appmsg&watermark=1&tp=webp&wxfrom=5&wx_lazy=1)

定义这些角色后，整个庭审仿佛“活”了过来，每个角色都仿佛有了自己的生命一般。我明显感觉到如下的进步：

- 专业角色系统：
    
    每个角色都有独立的专业思维模式，比如法官跟律师的思维是不一样的。
    
- 记忆与学习机制：
    
    角色能够"记住"案件材料并深度理解，不再出现搞错自己一方诉求的情况。
    
- 利益导向设计：
    
    严格按照各方真实利益对抗性辩论，不再出现为对方当事人辩护的情况。
    

## 4️⃣ 继续优化角色

当然，我们不会轻易满足。真实庭审，不仅需要几个“活”的角色，还需要有真实能力才行。

**“女娲”**不仅仅是定义角色，还可以帮我们优化**法官**跟**律师**的角色。

**首先是优化“法官”角色。**

现实中法官是具备穿透审判的思维。所谓穿透审判思维，即**能够透过案件表象，依据法律真实和证据规则，准确认定案件事实并作出符合法律精神和实质正义的裁判**。简言之，就是要让“法官”拿着X光一样判案。

为此，我告诉“女娲”：

```
激活女娲，优化法官角色，使其具备穿透审判思维。使其能够透过案件表象，依据法律真实和证据规则，准确认定案件事实并作出符合法律精神和实质正义的裁判。
```

很快，女娲对法官角色做了如下的优化：

```
<thought>  <principle>    ## 穿透审判核心框架    ### 穿透性思维原则    - **透过现象看本质**：不被表面争议迷惑，直击案件核心    - **穿透当事人策略**：识破诉讼技巧，看清真实争议    - **洞察法律关系实质**：超越形式条文，把握法律精神    - **预见判决效果**：前瞻性考虑判决的社会影响    ### 省略后续内容......  </principle>  <method>    ## 穿透审判操作方法    ### 案件穿透分析流程    ```mermaid    flowchart TD      A[接收案件信息] --> B[表面争议识别]      B --> C[深层矛盾挖掘]      C --> D[核心争议锁定]      D --> E[法律关系解构]      E --> F[证据价值评估]      F --> G[逻辑关系梳理]      G --> H[判决方案形成]      H --> I[社会效果预判]    ```    ### 省略后续内容......  </method></thought>
```

有了穿透审判思维的“法官”角色，可以主动发现案件事实真相，并进行提问！

**接下来优化“律师”角色。**

大家对律师第一印象是什么？也许是巧言善辩。

之所以大家对律师有这个印象，最主要的原因是律师讲究逻辑。那么对律师的优化，也就围绕逻辑进行。

我突然想到了李继刚老师的一段提示词：

```
Role: 逻辑学家# Profile:- author: 李继刚(Arthur)- 即刻ID: 李继刚- version: 0.2- language: 中文- description: 擅长分析对方表达观点的逻辑结构和逻辑漏洞。从论题、事实、结论、论证结构、基本假设、概念、立场等角度进行分析，输出观点的逻辑漏洞。## Goals:- 分析对方观点的逻辑结构- 揭示逻辑漏洞并输出## Constrains:- 严格遵守逻辑原则和规则- 基于事实和理性进行推理分析## Skills:- 掌握逻辑学的基本原理与方法- 运用逻辑推理分析观点的正确性与合理性- 发现逻辑漏洞并提出反驳意见## Workflows:1. 接收用户输入的观点信息2. 提取核心论题、事实, 隐含假设, 对方立场和结论3. 分析论证结构4. 定位逻辑漏洞## Initialization: 作为逻辑学家，我擅长分析观点的逻辑结构和逻辑漏洞，以揭示错误的推理和不合理的观点。我将用清晰和精确的语言与您对话，并从论题、事实、结论、论证结构、基本假设、概念、立场等多个角度进行分析。请告诉我您想要分析的观点，我将竭诚为您提供分析结果。
```

这段提示词可厉害了，我曾经拿他怼赢无数杠精，把这些能力赋予“律师”角色再合适不过。

为此，我告诉“女娲”：

```
激活女蜗，请先理解这段提示词，并将这项逻辑分析的能力赋予“律师”角色。// 上述的提示词，这里省略……
```

“女蜗”优化后的效果，如下：

```
<execution>  <rule>    ## 🧠 逻辑分析核心能力（极其重要！）    ⚠️逻辑分析核心能力（极其重要！）⚠️    🧠「律师核心竞争力-逻辑分析必须优先启动」    ——每个案件都必须执行完整逻辑分析流程：    ① 提取核心论题、事实、隐含假设、对方立场和结论    ② 分析论证结构：大前提、小前提、结论的逻辑严密性    ③ 定位逻辑漏洞：从逻辑学和法学双重维度识别致命缺陷    ④ 构建三段论反驳策略：基于发现的漏洞制定精准打击方案    ⚔️ 运用逻辑学基本原理分析观点正确性与合理性，发现逻辑漏洞并提出反驳意见    ## 📋 证据裁判主义    ⚠️ 证据裁判主义 ⚠️    🗂️「谨守卷宗绝对封闭原则」    ——所有陈述限于已质证之证据材料范围，拒绝对诉辩称述作任何超范围解释    ## ⚔️ 争议焦点主动识别义务    ⚠️ 争议焦点主动识别义务 ⚠️    ⚔️「启动《民诉法解释》第 105 条深层审查」    ——对相对方主张自动执行：    ① 请求权基础要件拆解（必须结合逻辑分析）    ② 举证责任分配合规性检测（识别证据逻辑矛盾）    ③ 待证事实真伪性反证（运用逻辑推理分析）    ## 省略以下内容……  </rule></execution>
```

## 5️⃣ 效果演示

经过一番操作，我们来看看实际效果。

这里我用一个真实的案件进行介绍，基本案情是：何某开叉车上路，撞伤了李某，李某起诉何某时，何某辩称是施工方的员工，请求法院列施工方为被告。施工方又辩称自己投保了一切险，请求法院将保险公司也列为被告（所有信息皆去敏处理）。

因此，本案各方的关系是：

1. 原告：李某（交通事故受害人）

2. 被告一：何某（交通事故施害人）

3. 被告二：施工方

4. 被告三：保险公司

接下来，展示一些精彩的瞬间～

**开始庭审**

![图片](https://mmbiz.qpic.cn/sz_mmbiz_png/G9EbG9MtFaiaRvF8t8JZ2PiaPlvx5udQOkyVTyyIC5OEmxaD1z5VjhahRlvsgrMjrYP17m9kXFY0FP8nQPGJvgVQ/640?wx_fmt=png&from=appmsg&watermark=1&tp=webp&wxfrom=5&wx_lazy=1)

系统自动激活“法官”角色，并开始庭审。

![图片](https://mmbiz.qpic.cn/sz_mmbiz_png/G9EbG9MtFaiaRvF8t8JZ2PiaPlvx5udQOkWFZZAyLqGQrzAH5YPuCQOT1Y3HkaaZFffCzZC0icup2W6tiaXrTBH97Q/640?wx_fmt=png&from=appmsg&watermark=1&tp=webp&wxfrom=5&wx_lazy=1)

法官指定由原告律师发言时，系统自行激活原告律师的角色，确保每个律师都是独立的，这就避免被告一的律师为被告二辩护的问题。

![图片](https://mmbiz.qpic.cn/sz_mmbiz_png/G9EbG9MtFaiaRvF8t8JZ2PiaPlvx5udQOkncSYibVcBbl2aDduYHTy5NeQ3exzC6d2hNNy8h3MDFhlB6icmVpM3kRw/640?wx_fmt=png&from=appmsg&watermark=1&tp=webp&wxfrom=5&wx_lazy=1)

用户只需要输入“继续”的指令，就可以推动庭审进行。此时会由法官决定由谁发言，轮到发言的角色会对法官的问题进行回应。

不难发现，使用 PromptX 的优势很明显！每个发言的角色都是独立的，确保了职责分离，更符合真实的场景。

以下是被告二、被告三的发言：

![图片](https://mmbiz.qpic.cn/sz_mmbiz_png/G9EbG9MtFaiaRvF8t8JZ2PiaPlvx5udQOkE4DzbHrhBKwUY7MxSOV0qrxkYVX9L7Qs16BYyuluepwE8wzPnXas6w/640?wx_fmt=png&from=appmsg&watermark=1&tp=webp&wxfrom=5&wx_lazy=1)

![图片](https://mmbiz.qpic.cn/sz_mmbiz_png/G9EbG9MtFaiaRvF8t8JZ2PiaPlvx5udQOkG0nKvMBhqHbichHX7ialBfNDYZZxuBMlM72Kf5t9CeKdXhPia3HMWhx1Q/640?wx_fmt=png&from=appmsg&watermark=1&tp=webp&wxfrom=5&wx_lazy=1)

被告律师辩护时，仅仅围绕案件事实展开，真正做到以事实为依据、以法律为准绳。

真实的庭审，也无外乎如此！

还记得，我上面优化过法官角色么？即让法官拥有穿透审判的能力，这个能力在这里成为现实了！

![图片](https://mmbiz.qpic.cn/sz_mmbiz_png/G9EbG9MtFaiaRvF8t8JZ2PiaPlvx5udQOkVpylP0aFwoBwmgG0Cjb9VPj59e7hITECkwkQsc3HYdlCGxfhslyRxw/640?wx_fmt=png&from=appmsg&watermark=1&tp=webp&wxfrom=5&wx_lazy=1)

法官角色，不再是一个主持人的角色，而是会主动发现争议焦点、主动探寻案件真相的审判者角色！这是让我非常惊喜的！

![图片](https://mmbiz.qpic.cn/sz_mmbiz_png/G9EbG9MtFaiaRvF8t8JZ2PiaPlvx5udQOk5RicdZ3yDf5aywde5I47WVbxPdDKyVzzOvbbaQaRn8IXXMxqyvKjE8A/640?wx_fmt=png&from=appmsg&watermark=1&tp=webp&wxfrom=5&wx_lazy=1)

不瞒各位，这是一个真实案件，我方代表被告二，我看AI律师的回答，自己都觉得很精彩。

针对被告二的强力辩论，被告一一方也完全不虚，大家看看：

![图片](https://mmbiz.qpic.cn/sz_mmbiz_png/G9EbG9MtFaiaRvF8t8JZ2PiaPlvx5udQOkKPUCq8Dzob8PxTpvorqa5h0heXJ6DcRhP7wC91ql4PfiaYzUaDJceMw/640?wx_fmt=png&from=appmsg&watermark=1&tp=webp&wxfrom=5&wx_lazy=1)

被告三律师的答复也是非常专业的！

质证环节，AI律师甚至可以发现原告起诉状中跟交通事故无关的疾病诊断，现实中很少有人可以发现这么细微的问题！

![图片](https://mmbiz.qpic.cn/sz_mmbiz_png/G9EbG9MtFaiaRvF8t8JZ2PiaPlvx5udQOk76ZZ4kJO5iaX9xxJVDDicr9RNUXxrrJXp1Kd1dWQ8iaIVsl0bfVd80IVQ/640?wx_fmt=png&from=appmsg&watermark=1&tp=webp&wxfrom=5&wx_lazy=1)

对于被告的问题，原告律师也做出了回应

(不管这个回应对不对，能回应本身就很值得表扬，不是么?)

![图片](https://mmbiz.qpic.cn/sz_mmbiz_png/G9EbG9MtFaiaRvF8t8JZ2PiaPlvx5udQOk51JUkPmic6UEeiaBb0MwAUzbFm0MUU2I2qwpYV6BBCOarhPpZ3NFgicDw/640?wx_fmt=png&from=appmsg&watermark=1&tp=webp&wxfrom=5&wx_lazy=1)

后续的环节这里就不逐一展示。

本案AI生成约5.7万字的庭审笔录跟判决书。

本文末会附上链接，感兴趣可自行查看。

**6️⃣ PromptX 模拟庭审的突出亮点**

**首先，是技术架构亮点：**

```
1. 智能角色切换机制：使用 promptx_action 工具实现动态角色激活，每次发言前自动切换到对应专业角色，确保每个角色都具备相应的专业知识和立场2. 严格的流程控制：法官绝对主导庭审进程，每次发言后必须指定下一发言人，单人发言原则，避免混乱，按照真实庭审流程设计：开庭 → 法庭调查 → 举证质证 → 法庭辩论 → 判决
```

**其次，是专业性亮点：**

```
1. 自动识别争议焦点：模拟真实的庭审，质证结束后自动生成争议焦点文件，后续辩论严格围绕争议焦点进行，体现了真实庭审中的争议焦点归纳环节。2. 角色专业化表现：每个角色都有明确的利益立场和专业背景，律师发言体现法律专业素养，引用具体法条，法官体现中立性和程序控制能力。3. 真实案例驱动：基于完整的案件材料（起诉状、证据、答辩状等），体现真实案件的复杂性和争议性。
```

**最后，是用户体验亮点：**

```
1. 简化的交互方式：用户只需输入"开始审理：案件名称"即可启动，输入"继续"推进庭审流程，大模型跟promptx自动处理复杂的角色切换和流程控制。2. 完整的文书生成：自动生成庭审笔录，完整记录庭审过程，生成专业的判决书，文书格式规范，符合司法实践。
```

最后的最后，附上本文干货分享，包括模拟法庭的提示词和上下文：

感谢 Deepractice 团队跟山哥的技术支持！

- **PromptX 项目地址**：
    
    github.com/Deepractice/PromptX
    
- **模拟法庭项目地址**：  
    
    github.com/jiangxia/ai-trial
    

(模拟法庭附有本案的庭审笔录，感兴趣的读者可以进一步前往阅读)

---

_🌟 欢迎_**加入深度实践分享你的案例**

![图片](https://mmbiz.qpic.cn/sz_mmbiz_jpg/G9EbG9MtFaiaRvF8t8JZ2PiaPlvx5udQOkxR38mibWdEaeagq6kxmvkKgr2d7zQycQAus18HQPITMZ7icXzKhpmq9w/640?wx_fmt=jpeg&watermark=1&tp=webp&wxfrom=5&wx_lazy=1)

### **我们诚挚邀请社区成员分享AI实践经验：**

- 📝 **提交方式**  
    
    通过项目 PR 添加案例
    
- 🎯 **分享内容**  
    
    项目介绍、使用心得、效果数据、经验总结
    
- 🏆 **展示平台**  
    
    在这里展示你的创新成果，获得社区认可
    
- 🤝 **互相学习**  
    
    与其他实践者交流经验，共同成长
    

> 让每个优质案例都成为社区的财富！