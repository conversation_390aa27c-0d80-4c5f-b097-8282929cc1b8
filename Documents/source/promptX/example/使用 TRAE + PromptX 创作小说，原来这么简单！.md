耗时一周，我写了一部88万字的长篇小说。

- 百万量级字数的小说，可以通过先设定，进行章节规划，再撰写内容，如此避免因上下文丢失导致的一致性问题。
    
- 透过设置多个角色，以其中一个主角色协调相应角色干活，就不用担心角色太多不易管理。
    
- 自动化流程。
    

![图片](https://mmbiz.qpic.cn/sz_mmbiz_jpg/G9EbG9MtFagqS3Qqyq8dwVlJLJ1YibHNTH7fShswITGGibEWicQr9wdX99kUvk3na7LKmicahibR8pwbic8ItRG3AnGw/640?wx_fmt=jpeg&from=appmsg&watermark=1&tp=webp&wxfrom=5&wx_lazy=1)

这部小说创作难度巨大，我要求：

1. 严格按照易经64卦的顺序，映射到新中国走向复兴的百年变局，每一卦都必须对应到特定的历史时期
    
2. 每一卦又包括6爻，每一爻辞还要对应到这个历史时期的6个阶段，也就是，一共386爻，要对应历史时期，还不能显得生硬！
    
3. 我希望模仿紫金陈、华杉的风格，制造悬念，让读者有欲望读下去
    
4. 我希望模仿华杉的风格，让小说不会显得晦涩难懂
    
5. 我希望融入百年未有大变局、中华民族伟大复兴等等的思考
    
6. 我希望体现易经作为中华文化源头的思考与体悟
    
7. ……
    

我就像许愿池的王八，想得恢宏壮阔，但如果靠我自己，我估计这辈子都写不出来！然而，这么高难度的小说，我用 AI 仅需要一周，这也太神奇了！

我是怎么做的呢？这一切都源于一位热心朋友的提示词。

当我看到该提示词时，我如获至宝，突然我意识到该提示词中涉及到诸多角色，我就萌生了使用 PromptX 进行二次创作的念头。

**说干就干，我用 PromptX 生成了9个角色，他们的职责分别是：**

```
🎭 创意策划层
```

其中，你会觉得角色这么多记不住，没事，我暖心的定义了**第0号角色 - 系统管理员（系统总监）。**

由他指定在什么阶段做什么事，你只需要根据他的提示操作就行。

那写小说具体包括哪些阶段呢？

**一、概念构建阶段**

```
1. **核心概念形成** [首席架构师]
```

大家还记得系统总监么？可以直接对他说：

```
系统总监，我现在要进行概念构建阶段，麻烦协调相应的角色与我对话
```

我们对系统总监的定义，就是由他协调角色，角色再多都不怕。

![图片](https://mmbiz.qpic.cn/sz_mmbiz_jpg/G9EbG9MtFaiaM0P2ianpML9HYJDSDk4gGiamIEs17myuDhA51bTzHTZX9SoYaPzZGPkDa0vDoxBKu0wCPFYwqiaULA/640?wx_fmt=jpeg&from=appmsg&watermark=1&tp=webp&wxfrom=5&wx_lazy=1)

然后你就依次跟角色对话，把我文章开头那些要求一股脑说给 AI 听。他们就会分析并给你生成基本的思路。

**二、详细规划阶段**

因为我一开始预计这部小说会达到 100 万字，但通用AI很难支持这个幅度的上下文。

所以，我使用 AI 先给我生成64卦的章节规划。

每个卦的章节规划包括以下内容：

```
**第一章：元亨利贞 (引子)**
```

这些内容你不用管，我就让他给我生成章节规划，具体的内容，是他给我生成的。你们说，是不是写得还挺好的！

章节规划是因场景而异，我这部小说篇幅太长了，所以必须先做好64卦规划，这样就不怕丢失上下文。如果想做长篇小说的，可以考虑这种方式。

**三、创作和修订阶段**

这个阶段，我比较佛系，我做成自动化。

```
当用户发出创作或优化指令时，系统将：
```

**四、过程示例**

以下，均使用目前我主力的国产客户端 TRAE[1]，以及在MCP市场可以搜索到的AI应用原生专业能力增强系统 PromptX[2] MCP：

![图片](https://mmbiz.qpic.cn/sz_mmbiz_jpg/G9EbG9MtFaiaM0P2ianpML9HYJDSDk4gGiauHtwMJwO9T9SCId8tobX1LaLia990MDpOnp6S0yGNwKddR9xTgpsUcA/640?wx_fmt=jpeg&watermark=1&tp=webp&wxfrom=5&wx_lazy=1)

当我说“创作泰卦”时，大模型会读取章节规划，根据章节规划进行创作

![图片](https://mmbiz.qpic.cn/sz_mmbiz_png/G9EbG9MtFagqS3Qqyq8dwVlJLJ1YibHNTic5I8tkmziarGTicH5nTQmR9CNNxAicUA4KoFptibDeTEInQwtRzVMMibIsA/640?wx_fmt=png&from=appmsg&watermark=1&tp=webp&wxfrom=5&wx_lazy=1)

创作完成后，大模型会激活相应的角色，对文章进行优化。

![图片](https://mmbiz.qpic.cn/sz_mmbiz_png/G9EbG9MtFagqS3Qqyq8dwVlJLJ1YibHNTjfbicnf0fQEGM2E0icicK3TLk9HEt2JI6YVFpRs9lTFviabTmX4ewl9WzQ/640?wx_fmt=png&from=appmsg&watermark=1&tp=webp&wxfrom=5&wx_lazy=1)

当然，我们可以主动地检查。

![图片](https://mmbiz.qpic.cn/sz_mmbiz_png/G9EbG9MtFagqS3Qqyq8dwVlJLJ1YibHNTopB09vRsr4IRwNoe23VYfAPEQbibVia8Kc212zkwKoDIvH5ERB7DNiaaw/640?wx_fmt=png&from=appmsg&watermark=1&tp=webp&wxfrom=5&wx_lazy=1)

大概率是符合章节规划的。

![图片](https://mmbiz.qpic.cn/sz_mmbiz_png/G9EbG9MtFagqS3Qqyq8dwVlJLJ1YibHNTFyoMYNJWe3XstUb34nSSygjAnF8RvROIvM2c9ibAZRDJys351t5PJ9A/640?wx_fmt=png&from=appmsg&watermark=1&tp=webp&wxfrom=5&wx_lazy=1)

如果不符合，他会告诉你，然后帮你修改。

![图片](https://mmbiz.qpic.cn/sz_mmbiz_png/G9EbG9MtFagqS3Qqyq8dwVlJLJ1YibHNTiaa53ciaQD4M2uBXJiaOxfP1icm8udP5Gx2h42o0xynicskUjrNA224BeBA/640?wx_fmt=png&from=appmsg&watermark=1&tp=webp&wxfrom=5&wx_lazy=1)

大家看完本文的思路，应该不难理解，如果有任何问题，可以在加入深度实践社区群后交流。

最后，对小说感兴趣的朋友，可以点击文章左下角阅读原文。

#### 引用链接

`[1]`TRAE: _https://trae.deepractice.ai_  
`[2]`PromptX: _https://github.com/Deepractice/PromptX_