## Augment 目前在用的 MCP 服务

```json
{
  "mcpServers": {
    "Sequential thinking": {
      "command": "npx",
      "args": [
        "-y",
        "@modelcontextprotocol/server-sequential-thinking"
      ]
    },
    "Playwright": {
      "command": "npx",
      "args": [
        "-y",
        "@playwright/mcp@latest"
      ]
    },
    "Exa Search": {
      "command": "npx",
      "args": [
        "-y",
        "exa-mcp-server"
      ],
      "env": {
        "EXA_API_KEY": "b226d29a-dc05-4977-bdda-5bc55d7328d4"
      }
    },
    "firecrawl-mcp": {
      "command": "npx",
      "args": [
        "-y",
        "firecrawl-mcp"
      ],
      "env": {
        "FIRECRAWL_API_KEY": "fc-a491e4cc8f3d4213808806c70a94449e"
      }
    },
    "TikHub.io API Docs": {
      "command": "npx",
      "args": [
        "-y",
        "apifox-mcp-server@latest",
        "--site-id=4705614"
      ]
    },
    "飞书 API - API 文档": {
      "command": "npx",
      "args": [
        "-y",
        "apifox-mcp-server@latest",
        "--site-id=532425"
      ]
    },
    "github": {
      "command": "npx",
      "args": [
        "-y",
        "@modelcontextprotocol/server-github"
      ],
      "env": {
        "GITHUB_PERSONAL_ACCESS_TOKEN": "****************************************"
      }
    },
    "context7": {
      "url": "https://mcp.context7.com/mcp",
      "type": "http"
    },
    "Puppeteer": {
      "command": "npx",
      "args": [
        "-y",
        "@modelcontextprotocol/server-puppeteer"
      ]
    },
    "Brave Search": {
      "command": "npx",
      "args": [
        "-y",
        "@modelcontextprotocol/server-brave-search"
      ],
      "env": {
        "BRAVE_API_KEY": "BSAXComZb3HInv5jJ7htVVZ6aZczcli"
      }
    },
    "promptx": {
      "command": "npx",
      "args": [
        "-y",
        "-f",
        "--registry",
        "https://registry.npmjs.org",
        "dpml-prompt@dev",
        "mcp-server"
      ]
    },
    "Desktop Commander": {
      "command": "npx",
      "args": [
        "-y",
        "@wonderwhy-er/desktop-commander@latest"
      ]
    },
    "mcp-deepwiki": {
      "command": "npx",
      "args": [
        "-y",
        "mcp-deepwiki@latest"
      ]
    },
    "tavily-remote-mcp": {
      "command": "npx",
      "args": [
        "-y",
        "mcp-remote",
        "https://mcp.tavily.com/mcp/?tavilyApiKey=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"
      ]
    },
    "shrimp-task-manager": {
      "command": "npx",
      "args": [
        "-y",
        "mcp-shrimp-task-manager"
      ],
      "env": {
        "DATA_DIR": "/Users/<USER>/Downloads/Ming-Digital-Garden/.shrimp",
        "TEMPLATES_USE": "zh",
        "ENABLE_GUI": "true",
        "PROJECT_AUTO_DETECT": "true"
      }
    }
  }
}
```

## 其他常用

```json
{
  "mcpServers": {
    "mcp-server-time": {
      "command": "uvx",
      "args": [
        "mcp-server-time",
        "--local-timezone=Asia/Shanghai"
      ]
    },
    "寸止": {
      "command": "寸止",
      "args": []
    },
    "mcp-feedback-enhanced": {
      "command": "uvx",
      "args": [
        "mcp-feedback-enhanced@latest"
      ]
    },
    "memory": {
      "command": "npx",
      "args": [
        "-y",
        "@modelcontextprotocol/server-memory"
      ],
      "env": {
        "MEMORY_FILE_PATH": "/Users/<USER>/Downloads/Ming-Digital-Garden/01-Memory/memory.json"
      }
    },
    "Context 7": {
      "command": "npx",
      "args": [
        "-y",
        "@upstash/context7-mcp@latest"
      ]
    },
    "github": {
      "command": "npx",
      "args": [
        "-y",
        "@modelcontextprotocol/server-github"
      ],
      "env": {
        "GITHUB_PERSONAL_ACCESS_TOKEN": "****************************************"
      },
      "alwaysAllow": [
        "search_repositories",
        "get_file_contents"
      ]
    }
  }
}
```

## 其他

```json
{
  "mcpServers": {
    "@magicuidesign/mcp": {
      "command": "npx",
      "args": ["-y", "@magicuidesign/mcp@latest"]
    }
  }
}
```

```json
{
  "mcpServers": {
    "codebase-mcp": {
      "command": "codebase-mcp",
      "env": {
        "GEMINI_API_KEY": "your-gemini-api-key-here"
      }
    },
    "Desktop Commander": {
      "command": "npx",
      "args": [
        "-y",
        "@wonderwhy-er/desktop-commander@latest"
      ]
    }
  }
}
```

```json
{
  "mcpServers": {
    "fetch": {
      "command": "npx",
      "args": [
        "-y",
        "@modelcontextprotocol/server-fetch"
      ]
    },
    "Task Manager": {
      "command": "npx",
      "args": [
        "-y",
        "@smithery/cli@latest",
        "run",
        "@kazuph/mcp-taskmanager",
        "--key",
        "704e47bf-f78e-453d-acc5-def8a5b5883c"
      ]
    },
    "Filesystem": {
      "command": "npx",
      "args": [
        "-y",
        "@modelcontextprotocol/server-filesystem",
        "/Users/<USER>/Downloads/Ming-Digital-Garden"
      ]
    }
  }
}
```

```json
{
  "mcpServers": {
    "tavily-remote-mcp": {
      "command": "npx -y mcp-remote https://mcp.tavily.com/mcp/?tavilyApiKey=<your-api-key>",
      "env": {}
    }
  }
}
```


### 新添加模板

```json
{
  "mcpServers": {
    "tavily-remote-mcp": {
      "command": "npx -y mcp-remote https://mcp.tavily.com/mcp/?tavilyApiKey=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",
      "env": {}
    }
  }
}
```

```json
{
  "mcpServers": {
    "Context 7": {
      "command": "npx",
      "args": [
        "-y",
        "@upstash/context7-mcp@latest"
      ]
    }
  }
}
```